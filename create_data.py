import cv2, os
import sys

print("🎥 FACE DATA COLLECTION SYSTEM")
print("=" * 40)

haar_file = 'haarcascade_frontalface_default.xml'
datasets = 'datasets'
sub_data = 'SPD'

print(f"👤 Collecting data for: {sub_data}")
print(f"📁 Dataset directory: {datasets}")

# Create directory structure
path = os.path.join(datasets, sub_data)
if not os.path.isdir(path):
    os.makedirs(path)
    print(f"✅ Created directory: {path}")
else:
    print(f"📂 Using existing directory: {path}")

(width, height) = (130, 100)

# Load face cascade
face_cascade = cv2.CascadeClassifier(haar_file)
if face_cascade.empty():
    print("❌ ERROR: Could not load face cascade file!")
    print(f"   Make sure {haar_file} exists in the current directory")
    sys.exit(1)
else:
    print("✅ Face cascade loaded successfully")

print("\n🔍 Searching for cameras...")

# Try different camera indices with better detection
webcam = None
working_camera_index = None

for camera_index in [0, 1, 2, 3, 4]:
    print(f"   Testing camera index {camera_index}...", end=" ")
    try:
        test_cam = cv2.VideoCapture(camera_index)
        if test_cam.isOpened():
            # Try to read a frame to confirm it's working
            ret, test_frame = test_cam.read()
            if ret and test_frame is not None:
                webcam = test_cam
                working_camera_index = camera_index
                print(f"✅ FOUND!")
                break
            else:
                print("❌ Opens but can't read")
                test_cam.release()
        else:
            print("❌ Not available")
            test_cam.release()
    except Exception as e:
        print(f"❌ Error: {e}")

if webcam is None:
    print("\n❌ NO CAMERA DETECTED!")
    print("\n🔧 SOLUTIONS:")
    print("1. 🔌 Connect a USB webcam")
    print("2. 📱 Use phone as webcam (DroidCam, EpocCam)")
    print("3. 🔒 Check camera permissions in Windows Settings")
    print("4. 📷 Test Windows Camera app first")
    print("5. 🔄 Close other apps using camera (Zoom, Teams, etc.)")
    print("\n💡 Run 'python setup_camera.py' for detailed setup help")
    sys.exit(1)

print(f"✅ Using camera at index {working_camera_index}")
print(f"📸 Will capture 30 images for {sub_data}")
print("👀 Look at the camera and press any key when ready...")
print("⚠️  Press ESC to exit anytime")

print("\n🎬 Starting capture in 3 seconds...")
cv2.waitKey(3000)  # Wait 3 seconds

count = 1
faces_captured = 0

print(f"\n📸 CAPTURING IMAGES (0/30)")
print("=" * 30)

while count <= 30:
    ret, im = webcam.read()
    if not ret:
        print("❌ Failed to read from camera!")
        break

    gray = cv2.cvtColor(im, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.3, 4)

    # Draw rectangles around detected faces
    for (x,y,w,h) in faces:
        cv2.rectangle(im,(x,y),(x+w,y+h),(0,255,0),2)
        face = gray[y:y + h, x:x + w]
        face_resize = cv2.resize(face, (width, height))

        # Save the face image
        image_path = os.path.join(path, f"{count}.png")
        cv2.imwrite(image_path, face_resize)
        faces_captured += 1

        # Progress feedback
        progress = f"({faces_captured}/30)"
        print(f"✅ Captured image {count} {progress}")
        count += 1
        break  # Only capture one face per frame

    # Show status on image
    status_text = f"Captured: {faces_captured}/30 - Press ESC to exit"
    cv2.putText(im, status_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    if len(faces) == 0:
        cv2.putText(im, "No face detected - Position yourself in frame", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

    cv2.imshow(f'Face Capture - {sub_data}', im)
    key = cv2.waitKey(100)  # Slower capture rate

    if key == 27:  # ESC key
        print(f"\n⚠️  Capture stopped by user at {faces_captured} images")
        break

webcam.release()
cv2.destroyAllWindows()

print(f"\n🎉 CAPTURE COMPLETE!")
print(f"✅ Successfully captured {faces_captured} images for {sub_data}")
print(f"📁 Images saved in: {path}")
print(f"🚀 Ready to run face recognition: python face_recognize.py")
