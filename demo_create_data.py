import cv2, os

print("Face Data Collection Demo")
print("=" * 40)

# Configuration from original create_data.py
haar_file = 'haarcascade_frontalface_default.xml'
datasets = 'datasets'  
sub_data = 'Elon'     

# Show the setup
print(f"Haar Cascade File: {haar_file}")
print(f"Dataset Directory: {datasets}")
print(f"Person Name: {sub_data}")

# Check if haar cascade file exists
if os.path.exists(haar_file):
    print(f"✓ Haar cascade file found: {haar_file}")
else:
    print(f"✗ Haar cascade file not found: {haar_file}")

# Show directory structure
path = os.path.join(datasets, sub_data)
print(f"Target Directory: {path}")

if os.path.isdir(path):
    print(f"✓ Directory exists: {path}")
    files = os.listdir(path)
    print(f"  Contains {len(files)} files")
    if files:
        print(f"  Sample files: {files[:5]}")  # Show first 5 files
else:
    print(f"✗ Directory does not exist: {path}")

# Show what the original script does
print("\nOriginal create_data.py functionality:")
print("1. Initializes webcam (camera index 1)")
print("2. Loads Haar cascade for face detection")
print("3. Captures 30 face images from webcam")
print("4. Saves detected faces as 130x100 grayscale images")
print("5. Names files sequentially (1.png, 2.png, etc.)")

# Show the face cascade classifier
face_cascade = cv2.CascadeClassifier(haar_file)
if not face_cascade.empty():
    print(f"✓ Face cascade classifier loaded successfully")
else:
    print(f"✗ Failed to load face cascade classifier")

print("\nNote: The original script requires a working webcam to capture live face data.")
print("The existing dataset already contains 30 images each for 'Elon' and 'Steve'.")
