#!/usr/bin/env python3
"""
Complete Face Recognition System Demo
=====================================
This script demonstrates the complete face recognition system
with both camera capture and recognition functionality.
"""

import cv2
import os
import sys

def check_system_status():
    """Check if the system is ready for face recognition"""
    print("🔍 SYSTEM STATUS CHECK")
    print("=" * 40)
    
    # Check OpenCV
    print(f"✅ OpenCV Version: {cv2.__version__}")
    
    # Check face module
    try:
        cv2.face.FisherFaceRecognizer_create()
        print("✅ OpenCV Face Module: Available")
    except:
        print("❌ OpenCV Face Module: Missing")
        return False
    
    # Check Haar cascade
    haar_file = 'haarcascade_frontalface_default.xml'
    if os.path.exists(haar_file):
        print(f"✅ Haar Cascade: {haar_file}")
    else:
        print(f"❌ Haar Cascade: Missing {haar_file}")
        return False
    
    # Check datasets
    datasets_dir = 'datasets'
    if os.path.exists(datasets_dir):
        people = [d for d in os.listdir(datasets_dir) if os.path.isdir(os.path.join(datasets_dir, d))]
        print(f"✅ Training Data: {len(people)} people found")
        for person in people:
            person_dir = os.path.join(datasets_dir, person)
            images = len([f for f in os.listdir(person_dir) if f.endswith('.png')])
            print(f"   - {person}: {images} images")
    else:
        print("❌ Training Data: No datasets directory")
        return False
    
    return True

def check_camera():
    """Check for available cameras"""
    print("\n📷 CAMERA STATUS")
    print("=" * 40)
    
    camera_found = False
    for i in range(5):
        try:
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"✅ Camera {i}: Available")
                    camera_found = True
                    cap.release()
                    break
            cap.release()
        except:
            continue
    
    if not camera_found:
        print("❌ No cameras detected")
        print("💡 Use simulation mode or set up camera")
    
    return camera_found

def show_menu():
    """Show the main menu"""
    print("\n🎯 FACE RECOGNITION SYSTEM")
    print("=" * 40)
    print("1. 📷 Capture Face Data (Real Camera)")
    print("2. 🎭 Simulate Face Data (No Camera)")
    print("3. 🧠 Train & Test Recognition")
    print("4. 🚀 Run Live Face Recognition")
    print("5. 🔧 Camera Setup Help")
    print("6. 📊 System Status")
    print("0. ❌ Exit")
    print()

def main():
    """Main application loop"""
    print("🎉 WELCOME TO FACE RECOGNITION SYSTEM")
    print("=" * 50)
    
    # Initial system check
    system_ready = check_system_status()
    camera_available = check_camera()
    
    if not system_ready:
        print("\n❌ System not ready. Please install required dependencies.")
        return
    
    while True:
        show_menu()
        choice = input("👉 Select option (0-6): ").strip()
        
        if choice == "1":
            if camera_available:
                print("\n🚀 Starting face data capture...")
                os.system("python create_data.py")
            else:
                print("\n❌ No camera available. Use option 2 for simulation or 5 for setup help.")
        
        elif choice == "2":
            print("\n🎭 Starting face data simulation...")
            os.system("python simulate_capture.py")
        
        elif choice == "3":
            print("\n🧠 Running face recognition test...")
            os.system("python test_face_recognition.py")
        
        elif choice == "4":
            if camera_available:
                print("\n🚀 Starting live face recognition...")
                print("⚠️  Press ESC in the camera window to exit")
                os.system("python face_recognize.py")
            else:
                print("\n❌ No camera available. Use option 5 for setup help.")
        
        elif choice == "5":
            print("\n🔧 Opening camera setup help...")
            os.system("python setup_camera.py")
            # Recheck camera after setup
            camera_available = check_camera()
        
        elif choice == "6":
            system_ready = check_system_status()
            camera_available = check_camera()
        
        elif choice == "0":
            print("\n👋 Goodbye!")
            break
        
        else:
            print("\n❌ Invalid option. Please try again.")
        
        input("\n⏸️  Press Enter to continue...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("💡 Try running individual scripts or check setup.")
