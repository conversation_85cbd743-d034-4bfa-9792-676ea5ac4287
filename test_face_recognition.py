import cv2, numpy, os

# Configuration
size = 4
haar_file = 'haarcascade_frontalface_default.xml'
datasets = 'datasets'

print('Training the face recognition model...')
print('=' * 50)

# Load training data
(images, labels, names, id) = ([], [], {}, 0)
for (subdirs, dirs, files) in os.walk(datasets):
    for subdir in dirs:
        names[id] = subdir
        print(f"Loading data for: {subdir}")
        subjectpath = os.path.join(datasets, subdir)
        image_count = 0
        for filename in os.listdir(subjectpath):
            path = subjectpath + '/' + filename 
            label = id
            img = cv2.imread(path, 0)
            if img is not None:
                images.append(img)
                labels.append(int(label))
                image_count += 1
        print(f"Loaded {image_count} images for {subdir}")
        id += 1

print(f"\nTotal training images: {len(images)}")
print(f"People in database: {list(names.values())}")

# Convert to numpy arrays
(width, height) = (130, 100)
(images, labels) = [numpy.array(lis) for lis in [images, labels]]

# Train the model
print("\nTraining Fisher Face Recognizer...")
model = cv2.face.FisherFaceRecognizer_create()
model.train(images, labels)
print("Training completed!")

# Load face cascade
face_cascade = cv2.CascadeClassifier(haar_file)

print('\n' + '=' * 50)
print('Face Recognition Model Summary:')
print('=' * 50)
print(f"Model Type: Fisher Face Recognizer")
print(f"Training Images: {len(images)}")
print(f"Number of People: {len(names)}")
print(f"People in Database:")
for person_id, name in names.items():
    print(f"  ID {person_id}: {name}")

# Test with one of the training images
print('\n' + '=' * 50)
print('Testing with sample images:')
print('=' * 50)

# Test with a few sample images from the dataset
test_results = []
for person_id, name in names.items():
    person_path = os.path.join(datasets, name)
    test_image_path = os.path.join(person_path, "1.png")  # Test with first image
    
    if os.path.exists(test_image_path):
        test_img = cv2.imread(test_image_path, 0)
        if test_img is not None:
            # Resize to match training size
            test_img_resized = cv2.resize(test_img, (width, height))
            
            # Make prediction
            prediction = model.predict(test_img_resized)
            predicted_id = prediction[0]
            confidence = prediction[1]
            predicted_name = names[predicted_id]
            
            print(f"Testing image: {test_image_path}")
            print(f"  Actual person: {name}")
            print(f"  Predicted person: {predicted_name}")
            print(f"  Confidence score: {confidence:.2f}")
            print(f"  Correct prediction: {'✓' if predicted_name == name else '✗'}")
            print()
            
            test_results.append({
                'actual': name,
                'predicted': predicted_name,
                'confidence': confidence,
                'correct': predicted_name == name
            })

# Summary
print('=' * 50)
print('Test Results Summary:')
print('=' * 50)
correct_predictions = sum(1 for result in test_results if result['correct'])
total_tests = len(test_results)
accuracy = (correct_predictions / total_tests) * 100 if total_tests > 0 else 0

print(f"Total tests: {total_tests}")
print(f"Correct predictions: {correct_predictions}")
print(f"Accuracy: {accuracy:.1f}%")

print('\nNote: Lower confidence scores indicate better matches.')
print('Confidence threshold in original code: 800')
print('(Predictions with confidence > 800 would be labeled as "Unknown")')
