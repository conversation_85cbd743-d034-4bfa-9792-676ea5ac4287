import cv2
import platform

print("Camera Detection Tool")
print("=" * 30)
print(f"Operating System: {platform.system()}")
print(f"OpenCV Version: {cv2.__version__}")
print()

# Check for cameras on different indices
cameras_found = []
for i in range(5):  # Check indices 0-4
    print(f"Testing camera index {i}...", end=" ")
    cap = cv2.VideoCapture(i)
    
    if cap.isOpened():
        # Try to read a frame
        ret, frame = cap.read()
        if ret and frame is not None:
            height, width = frame.shape[:2]
            print(f"✓ FOUND - Resolution: {width}x{height}")
            cameras_found.append(i)
        else:
            print("✗ Opens but can't read frames")
    else:
        print("✗ Not available")
    
    cap.release()

print()
if cameras_found:
    print(f"✓ {len(cameras_found)} camera(s) found at index(es): {cameras_found}")
    print("You can use any of these indices in your code.")
else:
    print("✗ No cameras detected!")
    print("\nTroubleshooting steps:")
    print("1. Connect a USB webcam")
    print("2. Check Windows Camera app works")
    print("3. Ensure no other apps are using the camera")
    print("4. Check camera permissions in Windows Settings")
    print("5. Update camera drivers")

print("\nTo test Windows Camera app:")
print("Press Win+S, type 'Camera', and open the Camera app")
