import cv2
import os
import numpy as np
import time

def create_simulated_face_data(person_name, num_images=30):
    """Create simulated face data for testing without camera"""
    
    print(f"🎭 SIMULATED FACE CAPTURE FOR: {person_name}")
    print("=" * 50)
    
    # Setup directories
    datasets = 'datasets'
    path = os.path.join(datasets, person_name)
    
    if not os.path.exists(datasets):
        os.makedirs(datasets)
    
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"✅ Created directory: {path}")
    else:
        print(f"📂 Using existing directory: {path}")
    
    # Image dimensions (same as real capture)
    width, height = 130, 100
    
    print(f"\n📸 Generating {num_images} simulated face images...")
    print("🔄 This simulates what would happen with a real camera")
    
    for i in range(1, num_images + 1):
        # Create a realistic-looking face simulation
        # Start with a face-like oval shape
        img = np.zeros((height, width), dtype=np.uint8)
        
        # Add face oval (head shape)
        center_x, center_y = width // 2, height // 2
        cv2.ellipse(img, (center_x, center_y), (40, 50), 0, 0, 360, 180, -1)
        
        # Add some variation to make each image unique
        variation = i * 3  # Different for each image
        
        # Add eyes
        eye_y = center_y - 15
        cv2.circle(img, (center_x - 15, eye_y), 3, 120, -1)  # Left eye
        cv2.circle(img, (center_x + 15, eye_y), 3, 120, -1)  # Right eye
        
        # Add nose
        nose_points = np.array([[center_x, center_y - 5], 
                               [center_x - 3, center_y + 5], 
                               [center_x + 3, center_y + 5]], np.int32)
        cv2.fillPoly(img, [nose_points], 140)
        
        # Add mouth
        cv2.ellipse(img, (center_x, center_y + 15), (8, 4), 0, 0, 180, 100, -1)
        
        # Add some noise for realism
        noise = np.random.normal(0, 10, (height, width))
        img = np.clip(img.astype(np.float32) + noise, 0, 255).astype(np.uint8)
        
        # Add slight rotation/position variation
        if i % 3 == 0:  # Every 3rd image, add slight transformation
            M = cv2.getRotationMatrix2D((center_x, center_y), (i % 10) - 5, 1.0)
            img = cv2.warpAffine(img, M, (width, height))
        
        # Save the image
        image_path = os.path.join(path, f"{i}.png")
        cv2.imwrite(image_path, img)
        
        # Progress indicator
        if i % 5 == 0 or i == num_images:
            progress = (i / num_images) * 100
            print(f"   Progress: {i}/{num_images} ({progress:.0f}%)")
        
        # Small delay to simulate real capture timing
        time.sleep(0.1)
    
    print(f"\n✅ SIMULATION COMPLETE!")
    print(f"📁 Created {num_images} simulated face images in: {path}")
    print(f"🔬 These images can be used to test the face recognition system")
    
    return path

def main():
    print("🎭 FACE CAPTURE SIMULATOR")
    print("=" * 40)
    print("This creates simulated face data for testing without a camera")
    print()
    
    # Get person name
    person_name = input("👤 Enter person name (or press Enter for 'SimulatedPerson'): ").strip()
    if not person_name:
        person_name = "SimulatedPerson"
    
    # Get number of images
    try:
        num_images = input("📸 Number of images to generate (default 30): ").strip()
        num_images = int(num_images) if num_images else 30
        if num_images < 1 or num_images > 100:
            num_images = 30
    except:
        num_images = 30
    
    print(f"\n🚀 Starting simulation for '{person_name}' with {num_images} images...")
    
    # Create the simulated data
    path = create_simulated_face_data(person_name, num_images)
    
    print(f"\n🎯 NEXT STEPS:")
    print("1. ✅ Simulated data created successfully")
    print("2. 🚀 Run: python face_recognize.py")
    print("3. 🧪 Test the face recognition system")
    print("4. 📱 When ready, set up real camera for live capture")
    
    return path

if __name__ == "__main__":
    main()
