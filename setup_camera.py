import cv2
import os
import platform
import subprocess
import sys

def check_camera_availability():
    """Check for available cameras and return the first working one"""
    print("🔍 Scanning for available cameras...")
    
    for i in range(10):  # Check more indices
        try:
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    height, width = frame.shape[:2]
                    print(f"✅ Camera found at index {i} - Resolution: {width}x{height}")
                    cap.release()
                    return i
            cap.release()
        except:
            continue
    
    return None

def install_phone_webcam_apps():
    """Provide instructions for phone-as-webcam setup"""
    print("\n📱 PHONE AS WEBCAM OPTIONS:")
    print("=" * 50)
    print("1. DroidCam (Free)")
    print("   - Download: https://www.dev47apps.com/droidcam/windows/")
    print("   - Install on both PC and phone")
    print("   - Connect via USB or WiFi")
    
    print("\n2. EpocCam (Free/Paid)")
    print("   - Download from App Store/Google Play")
    print("   - Install desktop client from elgato.com")
    
    print("\n3. OBS Virtual Camera (Advanced)")
    print("   - Install OBS Studio")
    print("   - Use phone with OBS mobile app")
    
    print("\n4. Windows Phone Link (Windows 11)")
    print("   - Built into Windows 11")
    print("   - Connect Android phone")

def check_windows_camera():
    """Try to open Windows Camera app"""
    print("\n📷 Testing Windows Camera App...")
    try:
        if platform.system() == "Windows":
            subprocess.run(["start", "microsoft.windows.camera:"], shell=True, check=False)
            print("✅ Windows Camera app launched")
            print("   If camera works there, the issue is with OpenCV permissions")
            return True
    except:
        pass
    return False

def create_virtual_dataset():
    """Create a virtual dataset for testing without camera"""
    print("\n🎭 Creating virtual test dataset...")
    
    # Create test person directory
    test_person = "TestPerson"
    datasets_dir = "datasets"
    person_dir = os.path.join(datasets_dir, test_person)
    
    if not os.path.exists(person_dir):
        os.makedirs(person_dir)
        print(f"✅ Created directory: {person_dir}")
    
    # Create placeholder images (black squares with text)
    import numpy as np
    
    for i in range(1, 31):
        # Create a 130x100 black image
        img = np.zeros((100, 130), dtype=np.uint8)
        
        # Add some pattern to make it recognizable
        cv2.putText(img, f"Test {i}", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)
        cv2.rectangle(img, (20, 20), (110, 80), (128, 128, 128), 1)
        
        # Save the image
        img_path = os.path.join(person_dir, f"{i}.png")
        cv2.imwrite(img_path, img)
    
    print(f"✅ Created 30 test images for {test_person}")
    return test_person

def main():
    print("🚀 CAMERA SETUP WIZARD")
    print("=" * 50)
    
    # Check OpenCV version
    print(f"OpenCV Version: {cv2.__version__}")
    print(f"Operating System: {platform.system()}")
    
    # Check for cameras
    camera_index = check_camera_availability()
    
    if camera_index is not None:
        print(f"\n✅ SUCCESS: Camera ready at index {camera_index}")
        print(f"You can now run: python create_data.py")
        return camera_index
    
    print("\n❌ NO CAMERA DETECTED")
    print("\n🔧 SOLUTIONS:")
    print("=" * 30)
    
    print("\n1. 🔌 CONNECT USB WEBCAM")
    print("   - Plug in external USB camera")
    print("   - Wait for driver installation")
    print("   - Run this script again")
    
    print("\n2. 📱 USE PHONE AS WEBCAM")
    install_phone_webcam_apps()
    
    print("\n3. 🔒 CHECK PERMISSIONS")
    print("   - Windows Settings → Privacy → Camera")
    print("   - Enable 'Let apps access your camera'")
    print("   - Enable for Python/Command Prompt")
    
    print("\n4. 🧪 TEST WITH VIRTUAL DATA")
    create_virtual_dataset()
    
    # Try Windows Camera
    check_windows_camera()
    
    print("\n" + "=" * 50)
    print("💡 NEXT STEPS:")
    print("1. Set up a camera using options above")
    print("2. Run this script again to verify")
    print("3. Then run: python create_data.py")
    
    return None

if __name__ == "__main__":
    camera_index = main()
    
    if camera_index is not None:
        # Update the create_data.py with the working camera index
        print(f"\n🔧 Updating create_data.py to use camera index {camera_index}...")
        # This would be done by the main script
    else:
        print("\n⚠️  Camera setup incomplete. Please follow the solutions above.")
